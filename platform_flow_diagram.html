<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>融合采购平台 - 信息流与资金流图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .flow-section {
            margin-bottom: 50px;
        }

        .section-title {
            font-size: 2em;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #4facfe;
            display: flex;
            align-items: center;
        }

        .section-title::before {
            content: '';
            width: 8px;
            height: 40px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            margin-right: 15px;
            border-radius: 4px;
        }

        .diagram-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
        }

        .roles-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .roles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .role-card {
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #4facfe;
            transition: transform 0.3s ease;
        }

        .role-card:hover {
            transform: translateY(-5px);
        }

        .role-card h4 {
            color: #333;
            margin-bottom: 5px;
            font-size: 1.1em;
        }

        .mermaid {
            text-align: center;
            background: white;
        }

        .legend {
            background: #f0f8ff;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border-left: 4px solid #4facfe;
        }

        .legend h4 {
            color: #333;
            margin-bottom: 10px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 3px;
            margin-right: 10px;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }
            
            .section-title {
                font-size: 1.5em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>融合采购平台</h1>
            <p>信息流与资金流可视化分析</p>
        </div>

        <div class="content">
            <!-- 平台角色介绍 -->
            <div class="roles-section">
                <h2 class="section-title">平台参与角色</h2>
                <div class="roles-grid">
                    <div class="role-card">
                        <h4>商品生产商</h4>
                        <p>负责商品的实际生产制造</p>
                    </div>
                    <div class="role-card">
                        <h4>商品供应商</h4>
                        <p>参与招标并提供商品供应</p>
                    </div>
                    <div class="role-card">
                        <h4>采购单位</h4>
                        <p>发起采购需求和招标流程</p>
                    </div>
                    <div class="role-card">
                        <h4>政府财政</h4>
                        <p>负责采购资金的最终付款</p>
                    </div>
                    <div class="role-card">
                        <h4>采购平台</h4>
                        <p>提供招标、订单管理等服务</p>
                    </div>
                    <div class="role-card">
                        <h4>担保机构</h4>
                        <p>为供应商提供回款担保服务</p>
                    </div>
                    <div class="role-card">
                        <h4>第三方垫资机构</h4>
                        <p>在政府延迟付款时提供资金</p>
                    </div>
                    <div class="role-card">
                        <h4>分账平台</h4>
                        <p>负责资金的分配和结算</p>
                    </div>
                </div>
            </div>

            <!-- 信息流图 -->
            <div class="flow-section">
                <h2 class="section-title">信息流程图</h2>
                <div class="diagram-container">
                    <div class="mermaid">
                        graph TD
                            A[采购单位] -->|1. 发起招标| B[采购平台]
                            B -->|2. 发布招标信息| C[商品供应商]
                            C -->|3. 参与投标| B
                            B -->|4. 招标结果| A
                            A -->|5. 确定供应商| C
                            A -->|6. 签订合同| C
                            C -->|7. 确定生产商| D[商品生产商]
                            C -->|8. 下达生产订单| D
                            D -->|9. 生产进度反馈| C
                            C -->|10. 供货信息| A
                            C -->|11. 签订担保协议| E[担保机构]
                            E -->|12. 担保信息| F[第三方垫资机构]
                            A -->|13. 订单信息| G[分账平台]
                            H[政府财政] -->|14. 付款信息| G
                            G -->|15. 分账信息| C
                            G -->|16. 分账信息| D

                            classDef procurement fill:#e1f5fe,stroke:#01579b,stroke-width:2px
                            classDef supplier fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
                            classDef financial fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
                            classDef platform fill:#fff3e0,stroke:#e65100,stroke-width:2px

                            class A procurement
                            class C,D supplier
                            class E,F,G,H financial
                            class B platform
                    </div>
                </div>
                
                <div class="legend">
                    <h4>信息流说明</h4>
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #e1f5fe;"></div>
                        <span>采购方 - 发起采购需求和管理流程</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #f3e5f5;"></div>
                        <span>供应方 - 提供商品和服务</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #e8f5e8;"></div>
                        <span>金融方 - 提供资金保障和结算</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #fff3e0;"></div>
                        <span>平台方 - 提供技术和管理服务</span>
                    </div>
                </div>
            </div>

            <!-- 资金流图 -->
            <div class="flow-section">
                <h2 class="section-title">资金流程图</h2>
                <div class="diagram-container">
                    <div class="mermaid">
                        graph TD
                            A[政府财政] -->|按时付款| B[分账平台]
                            A -->|延迟付款| C[担保机构]
                            C -->|启动担保| D[第三方垫资机构]
                            D -->|垫付资金| B
                            B -->|资金分配| E[商品供应商]
                            B -->|资金分配| F[商品生产商]
                            B -->|平台服务费| G[采购平台]
                            B -->|担保费用| C
                            B -->|垫资利息| D
                            A -->|最终还款| D
                            
                            H[采购单位] -.->|合同金额确认| B
                            E -.->|供货确认| B
                            F -.->|生产确认| B

                            classDef government fill:#ffebee,stroke:#c62828,stroke-width:3px
                            classDef platform fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
                            classDef supplier fill:#f1f8e9,stroke:#388e3c,stroke-width:2px
                            classDef financial fill:#fff8e1,stroke:#f57c00,stroke-width:2px
                            classDef info fill:#f5f5f5,stroke:#757575,stroke-width:1px,stroke-dasharray: 5 5

                            class A government
                            class B,G platform
                            class E,F,H supplier
                            class C,D financial
                    </div>
                </div>

                <div class="legend">
                    <h4>资金流说明</h4>
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #ffebee;"></div>
                        <span>政府资金 - 采购资金的最终来源</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #e3f2fd;"></div>
                        <span>平台资金 - 资金分配和平台费用</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #f1f8e9;"></div>
                        <span>供应商资金 - 商品供应和生产费用</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #fff8e1;"></div>
                        <span>金融服务 - 担保和垫资服务费用</span>
                    </div>
                    <p style="margin-top: 15px; color: #666; font-style: italic;">
                        实线表示资金流向，虚线表示信息确认流程
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化 Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });
    </script>
</body>
</html>
